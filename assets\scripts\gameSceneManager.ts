// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

const { ccclass, property } = cc._decorator;

import { globalVariables } from './utils/GlobalVariables';
import { AppConstants } from './utils/constants';
import Request from './apis/api';
import { $_setShareInfo } from './utils/generalUtil';
@ccclass
export default class NewClass extends cc.Component {
  @property(cc.Node)
  BackGround: cc.Node = null;

  @property(cc.Node)
  ScrollContent: cc.Node = null;

  @property(cc.Node)
  sceneTouchElement: cc.Node = null;

  @property(cc.Node)
  timerLaber: cc.Node = null;

  @property(cc.Node)
  foundElementStatisticsLabel: cc.Node = null;

  @property(cc.Node)
  maskSuccess: cc.Node = null;

  @property(cc.Node)
  maskFail: cc.Node = null;

  @property(cc.Node)
  maskLottery: cc.Node = null;

  @property(cc.Node)
  maskMessage: cc.Node = null;

  winOrfailFlag: boolean = false;

  passGameTime: number = 0;
  extraTime: number = 0; // 观看视频获得的额外时间
  addTime: number = 30; // 看视频增加的时间

  partHeight: number = 400; // 每个部分的高度
  numberOfParts: number = 5; // 切割的部分数量
  // LIFE-CYCLE CALLBACKS:
  scrollView: cc.ScrollView;

  private element3: cc.Node = null;
  private carSpeed: number = 800; // 移动速度（像素/秒）
  numberRound: number = 0; // 小车行驶的圈数
  carMoveEnabled: boolean = false; // 小车动画是否开启
  totalTime: number = 10; // 关卡时长

  backLevelSelectScene() {
    globalVariables.showMarkBlack = false;
    cc.director.loadScene(AppConstants.LEVEL_SELECT_SCENE);
  }
  onLoad() {
    this.adjustCanvasSize();

    // 第四关特殊处理
    if (globalVariables.currentLevel == 4) {
      // 获取三个子组件
      const element1 = this.sceneTouchElement.children[0];
      const element2 = this.sceneTouchElement.children[1];
      this.element3 = this.sceneTouchElement.children[2];

      // 设置层级顺序
      element1.zIndex = 1;
      element2.zIndex = 1;
    }
    // 先将BackGround，sceneTouchElement中的element_1、element_2子组件的sprite frame置空
    this.BackGround.getComponent(cc.Sprite).spriteFrame = null;
    this.sceneTouchElement
      .getChildByName('element_1')
      .getComponent(cc.Sprite).spriteFrame = null;
    this.sceneTouchElement
      .getChildByName('element_2')
      .getComponent(cc.Sprite).spriteFrame = null;

    const totalParts = 3;
    let yOffset = 0;

    // 顺序加载所有背景图片
    this.loadImagesSequentially(totalParts, yOffset).then(totalHeight => {
      // 调整 BackGround 的高度为总高度
      this.BackGround.height = totalHeight;
      this.ScrollContent.height = totalHeight;
      cc.log(`所有图片加载完成，总高度为: ${totalHeight}`);
    });

    this.sceneTouchElement.zIndex = 1;
    // 重置游戏
    this.resetGame();
    $_setShareInfo(globalVariables.shareInfo);
    cc.sp.enableLabelRetina = true;
    cc.sp.labelRetinaScale = 2;
  }
  // 如果当前是第四关时，将3张切图复制到BackGround中
  async loadImagesSequentially(
    totalParts: number,
    yOffset: number
  ): Promise<number> {
    let totalHeight = 0;
    for (let i = 0; i < totalParts; i++) {
      const partName =
        'coreGameSceneMaterial/image/level' +
        globalVariables.currentLevel +
        '/bg/' +
        (i + 1);
      const spriteFrame = await this.loadSpriteFrame(partName); // 等待图片加载完成

      const bgPart = new cc.Node(`BG_Part_${i}`);
      const sprite = bgPart.addComponent(cc.Sprite);
      sprite.spriteFrame = spriteFrame;

      // 设置 bgPart 锚点和位置
      bgPart.anchorY = 1;
      bgPart.y = yOffset;
      bgPart.x = 0;
      yOffset -= spriteFrame.getRect().height; // 累加偏移量
      totalHeight += spriteFrame.getRect().height;

      this.BackGround.addChild(bgPart); // 将图片节点添加到 BackGround
    }

    return totalHeight; // 返回总高度
  }

  loadSpriteFrame(path: string): Promise<cc.SpriteFrame> {
    return new Promise((resolve, reject) => {
      cc.resources.load(path, cc.SpriteFrame, (err, asset) => {
        if (err) {
          cc.error(`加载图片失败: ${path}, 错误: ${err}`);
          reject(err);
          return;
        }
        resolve(asset as cc.SpriteFrame); // 成功返回 cc.SpriteFrame
      });
    });
  }
  start() {
    this.schedule(this.updateGameTime, 1);
  }
  resetGame() {
    this.maskFail.active = false;
    this.maskSuccess.active = false;
    this.winOrfailFlag = false;
    globalVariables.currentFoundElement = 0;
    globalVariables.currentFoundElementIndexArray = [0, 0, 0, 0, 0, 0];
    this.passGameTime = 0;
    this.extraTime = 0; // 重置加时
  }
  updateGameTime() {
    // 停止第四关小汽车动画
    if (
      globalVariables.currentLevel == 4 &&
      globalVariables.currentFoundElementIndexArray[2] == 1
    ) {
      this.carMoveEnabled = false;
    }
    let shouldFoundElement =
      globalVariables.LevelShouldFoundElementArray[
        globalVariables.currentLevel - 1
      ];

    // 1. 更新已选中的元素个数
    this.foundElementStatisticsLabel.getComponent(cc.Label).string =
      globalVariables.currentFoundElement + '/' + shouldFoundElement;
    // 2.判断是否游戏完成
    if (globalVariables.currentFoundElement == shouldFoundElement) {
      // 游戏执行成功的后续逻辑
      if (!this.winOrfailFlag) {
        let index = globalVariables.currentLevel;

        // 设置关卡打开数组  index会比正常数组下标多1， 相当于执行了加1操作
        globalVariables.passLevelArray[index] = 1;

        // 记录当前关卡总耗时（包含加时）
        globalVariables.passTimeArray[index - 1] = this.passGameTime;

        // 2. 游戏数据上传 - completionTime包含加时
        Request.updatePassLevelData({
          levels: [
            {
              completed: true,
              completionTime: this.passGameTime, // 包含加时的总时间
              levelNumber: globalVariables.currentLevel,
            },
          ],
        });
        this.unschedule(this.updateGameTime);
      }

      this.winOrfailFlag = true;
      return;
    }

    // 3. 增加游戏时间
    this.passGameTime++;

    // 4. 游戏失败函数的执行, totalTime + extraTime秒后游戏失败
    let currentTotalTime = this.totalTime + this.extraTime;
    if (this.passGameTime >= currentTotalTime) {
      this.maskFail.active = true;
      this.unschedule(this.updateGameTime);
      globalVariables.currentFoundElementIndexArray = [0, 0, 0, 0, 0, 0, 0, 0];
      if (!this.winOrfailFlag) {
        // setTimeout(() => {
        //   globalVariables.showMarkBlack = false;
        //   cc.director.loadScene(AppConstants.LEVEL_SELECT_SCENE);
        // }, 3000);
      }

      this.winOrfailFlag = true;
    }
    // 5. 设置时间显示
    // 剩余时间 = 总时间（包含加时）- 已用时间
    let time = currentTotalTime - this.passGameTime;
    const minutes = Math.floor(time / 60);
    const seconds = time % 60;
    const formattedSeconds = seconds < 10 ? `0${seconds}` : seconds.toString();
    this.timerLaber.getComponent(
      cc.Label
    ).string = `${minutes}:${formattedSeconds}`;
  }

  /**
   * 观看视频后增加游戏时长
   */
  addExtraTimeAfterVideo() {
    // 增加额外时间
    this.extraTime += this.addTime;

    // 重新启动游戏，隐藏失败弹窗
    this.maskFail.active = false;
    this.winOrfailFlag = false;

    // 重新开始计时
    this.schedule(this.updateGameTime, 1);

    cc.log(
      `观看视频成功，增加${this.addTime}秒游戏时间，当前总加时：${this.extraTime}秒`
    );
  }

  adjustCanvasSize() {
    const designResolution = cc.view.getDesignResolutionSize();
    const screenSize = cc.view.getVisibleSize();

    // 计算设计分辨率宽高比和屏幕宽高比
    const designAspect = designResolution.width / designResolution.height;
    const screenAspect = screenSize.width / screenSize.height;

    if (screenAspect > designAspect) {
      // 屏幕更宽：按高度适配（避免左右黑边）
      cc.view.setDesignResolutionSize(
        designResolution.width,
        designResolution.height,
        cc.ResolutionPolicy.FIXED_WIDTH
      );
    } else {
      // 屏幕更窄：按宽度适配（避免上下黑边）
      cc.view.setDesignResolutionSize(
        designResolution.width,
        designResolution.height,
        cc.ResolutionPolicy.FIXED_HEIGHT
      );
    }
    // cc.log(`设计分辨率: ${cc.view.getDesignResolutionSize()}`);
    // cc.log(`逻辑像素尺寸: ${cc.view.getVisibleSize()}`);
    // cc.log(`物理像素尺寸: ${cc.view.getFrameSize()}`);
  }
  protected onDestroy(): void {
    globalVariables.currentFoundElement = 0;
    this.unschedule(() => {});
  }
  update(dt: number) {
    if (
      globalVariables.currentLevel != 4 ||
      !this.carMoveEnabled ||
      !this.element3
    )
      return;

    // 获取父节点尺寸（锚点顶部）
    const parentHeight = this.BackGround.height;

    // 向上移动车辆（y值减小）
    this.element3.y += this.carSpeed * dt;

    // 计算边界（锚点顶部坐标系）
    const topBoundary = this.element3.height / 2; // 完全移出顶部
    const bottomBoundary = -(parentHeight + this.element3.height / 2); // 完全移出底部

    // 当完全移出顶部时，重置到底部
    if (this.element3.y > topBoundary) {
      this.element3.y = bottomBoundary;
      this.numberRound++;
    }
    if (this.numberRound >= 4) {
      this.carMoveEnabled = false;
      this.element3.active = false;
    }
  }
}
